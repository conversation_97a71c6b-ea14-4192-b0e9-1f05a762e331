package com.dcai.aixg.impl;


import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

import java.time.LocalDateTime;
import java.util.List;

import com.dcai.aixg.domain.aiagent.node.Node;
import com.dcai.aixg.dto.NodeDTO;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.api.ExternalAPI;
import com.dcai.aixg.api.NodeAPI;
import com.dcai.aixg.domain.aiagent.node.NodeRpt;
import com.dcai.aixg.domain.broker.Broker;
import com.dcai.aixg.domain.broker.BrokerRpt;
import com.dcai.aixg.domain.order.Order;
import com.dcai.aixg.domain.order.OrderRpt;
import com.dcai.aixg.domain.task.Report;
import com.dcai.aixg.domain.task.Task;
import com.dcai.aixg.domain.task.TaskRpt;
import com.dcai.aixg.domain.task.Write;
import com.dcai.aixg.dto.OrderDTO;
import com.dcai.aixg.dto.task.ReportDetailDTO;
import com.dcai.aixg.dto.task.TaskDTO;
import com.dcai.aixg.dto.task.WriteDetailDTO;
import com.dcai.aixg.kerApi.KerApi;
import com.dcai.aixg.pro.ApiEditBrokerPO;
import com.dcai.aixg.pro.OrderPayCallBackPO;
import com.dcai.aixg.pro.UpdateCityPo;
import com.dcai.aixg.pro.task.CreateTaskCallBackPO;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.utils.StringUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RefreshScope
@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class ExternalImpl implements ExternalAPI {

    private final OrderRpt orderRpt;
    private final BrokerRpt brokerRpt;
    private final TaskRpt taskRpt;
    private final KerApi kerApi;
    private final NodeRpt nodeRpt;

    @Override
    public ApiResponse<List<OrderDTO>> orderPayCallBack(OrderPayCallBackPO po) {
        List<Order> orders = orderRpt.findAndLockByKerOrderNo(po.getOrderNo());
        for (Order order : orders) {
            order.payCallBack(po);
        }
        return ApiResponse.succ(convert2DTO(orders, new OrderDTO()));
    }

    @Override
    public ApiResponse<String> updateCity(UpdateCityPo po) {
        Broker broker = brokerRpt.findById(po.getUserId()).orElse(null);
        if (broker == null) {
            return succ(null);
        }
        if (StringUtils.isBlank(broker.getCityId())) {
            broker.setCityId(po.getCityId());
            broker.setCityName(po.getCityName());
        }
        return succ(broker.getCityId());
    }

    @Override
    public ApiResponse<String> updateUserInfo(ApiEditBrokerPO po) {
        Broker broker = brokerRpt.findById(po.getUserId()).orElse(null);
        if (broker == null) {
            return succ(null);
        }
        broker.edit(po);
        return succ();
    }

    @Override
    public ApiResponse<TaskDTO> createTaskCallBack(CreateTaskCallBackPO po) {
        log.info("ExternalImpl.createTaskCallBack, po={}", JSONObject.toJSONString(po));
        Task task = null;
        if (po.getTaskType() == TaskDTO.Type.REPORT) {
            List<Report> reports = taskRpt.findAndLockByReportId(po.getTaskId());
            if (reports == null || reports.size() == 0) return succ(null);
            Report report = reports.get(0);
            report.handleCallBack4Create(po);
            if (po.getTaskStatus() == TaskDTO.Status.DONE) {
                ReportDetailDTO result = kerApi.reportDetail(report.getBroker().getKerId(), po.getTaskId());
                report.buildTaskDetail(po.getMsg(), null, result.getContent(), LocalDateTime.now());
                report.handleSendMessage();
            }
            task = report;
        }  else if (po.getTaskType() == TaskDTO.Type.CRIC_DATA) {
            List<Node> nodes = nodeRpt.findByProcessInfo3(po.getTaskId());
            if (CollectionUtils.isEmpty(nodes)) {
                return succ();
            }
            Node node = nodes.get(0);
            String content = po.getMsg();
            NodeDTO.Status status = NodeDTO.Status.FAILED;
            if (po.getTaskStatus() == TaskDTO.Status.DONE) {
                WriteDetailDTO dto = kerApi.writeDetail(node.getRequest().getLong("userId"), node.getProcessInfo2());
                content = dto.getContent();
                status = NodeDTO.Status.SUCCESS;
            }
            getBean(NodeAPI.class).receiveAsyncResponse(node.getId(), status, content);
        } else if (po.getTaskType() == TaskDTO.Type.WRITE) {
            List<Node> nodes = nodeRpt.findByProcessInfo3(po.getTaskId());
            if (CollectionUtils.isEmpty(nodes)) {
                return succ();
            }
            Node node = nodes.get(0);
            String content = po.getMsg();
            NodeDTO.Status status = NodeDTO.Status.FAILED;
            if (po.getTaskStatus() == TaskDTO.Status.DONE) {
                WriteDetailDTO dto = kerApi.writeDetail(node.getRequest().getLong("userId"), node.getProcessInfo2());
                content = dto.getContent();
                status = NodeDTO.Status.SUCCESS;
            }
            getBean(NodeAPI.class).receiveAsyncResponse(node.getId(), status, content);
        }
        if (task == null) return succ(null);
        return succ(convert2DTO(task, new TaskDTO()));
    }

    public ApiResponse<TaskDTO> testHandleSendMessage(String writeId) {
        List<Write> writes = taskRpt.findAndLockByArticleId(writeId);
        if (writes == null || writes.size() == 0) return succ(null);
        Write write = writes.get(0);
        write.handleSendMessage();
        return succ(convert2DTO(write, new TaskDTO()));
    }
}

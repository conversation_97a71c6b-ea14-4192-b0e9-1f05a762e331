package com.dcai.aixg.domain.task;

import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.domain.broker.Broker;
import com.dcai.aixg.dto.FlowDTO;
import com.dcai.aixg.dto.task.TaskDTO;
import com.dcai.aixg.dto.task.WriteDetailDTO;
import com.dcai.aixg.pro.task.CreateTaskCallBackPO;
import com.ejuetc.commons.base.component.OssComponent;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.usertype.ListUT;
import com.ejuetc.commons.base.utils.StringUtils;
import com.ejuetc.consumer.api.delegation.ApiQueryListPO;
import com.ejuetc.consumer.api.delegation.DelegationAPI;
import com.ejuetc.consumer.web.vo.DelegationVO;
import com.ejuetc.saasapi.sdk.SaaSApiSDK;
import com.microsoft.playwright.*;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.ScreenshotType;
import com.microsoft.playwright.options.WaitUntilState;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Parameter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.spring.SpringUtil.getProperty;

/**
 * 任务
 */
@Entity
@Table(name = "tb_task")
@Comment("任务")
@Where(clause = "logic_delete = 0")
@NoArgsConstructor
@Getter
@Slf4j
@Accessors(chain = true)
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", columnDefinition = "enum('REPORT','WRITE') COMMENT '任务类型'")
public abstract class Task extends BaseEntity<Task> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", columnDefinition = "bigint(20) COMMENT 'ID'")
    private Long id;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "broker_id", columnDefinition = "bigint(20) COMMENT '经纪人ID'")
    protected Broker broker;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, insertable = false, updatable = false)
    protected TaskDTO.Type type;

    @Enumerated(EnumType.STRING)
    @Column(name = "sub_type", nullable = false, insertable = false, updatable = false)
    protected TaskDTO.SubType subType;

    @Column(name = "write_type", columnDefinition = "varchar(16) COMMENT '文章类型; 1:房源推荐软文 2:购房指南 3:行业热点评论'")
    protected String writeType;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    protected TaskDTO.Status status = TaskDTO.Status.ING;

    @Column(name = "msg", columnDefinition = "varchar(255) COMMENT '任务信息'")
    protected String msg;

    @Column(name = "open_id", columnDefinition = "varchar(255) COMMENT '消息接收openId'")
    protected String openId;

    @Column(name = "ask", columnDefinition = "varchar(255) COMMENT '话题'")
    protected String ask;

    @Column(name = "topic", columnDefinition = "varchar(255) COMMENT '写作主题'")
    protected String topic;

    @Column(name = "title", columnDefinition = "varchar(255) COMMENT '标题'")
    protected String title;
    
    @Column(name = "summary", columnDefinition = "text COMMENT '简短文章正文'")
    protected String summary;

    @Column(name = "content", columnDefinition = "MEDIUMTEXT COMMENT '内容'")
    protected String content;

    @Type(value = ListUT.class, parameters = {@org.hibernate.annotations.Parameter(name = "splitChar", value = ","), @Parameter(name = "elementType", value = "java.lang.String")})
    @Column(name = "house_ids", columnDefinition = "varchar(255) COMMENT '房源ID列表'")
    protected List<String> houseIds = Collections.emptyList();

    @Type(value = ListUT.class, parameters = @org.hibernate.annotations.Parameter(name = "splitChar", value = "\n"))
    @Column(name = "house_infos", columnDefinition = "MEDIUMTEXT COMMENT '房源信息列表'")
    protected List<String> houseInfos = new ArrayList<>();

    @Column(name = "completion_time", columnDefinition = "datetime(6) COMMENT '完成时间'")
    protected LocalDateTime completionTime;
    
    @Column(name = "flow_id", columnDefinition = "bigint(20) COMMENT '流程ID'")
    protected Long flowId;
    
    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", columnDefinition = "bigint(20) COMMENT '父任务'")
    protected Task parent;
    
    public void handleCallBack4Create(CreateTaskCallBackPO po) {
    	this.status = po.getTaskStatus();
    	this.msg = po.getMsg();
    }
    
    public void handleCallBack4Create(FlowDTO.Status status) {
    	if (status == FlowDTO.Status.SUCCESS) {
    		this.status = TaskDTO.Status.DONE;
    	} else if (status == FlowDTO.Status.FAILED) {
    		this.status = TaskDTO.Status.FAIL;
    	} else {
    		this.status = TaskDTO.Status.UNKNOW;
    	}
    }
    
    public void setLogicDelete() {
    	this.logicDelete = true;
    }
    
    public void buildTaskDetail(String title, String summary, String content, LocalDateTime completionTime) {
    	this.title = title;
    	this.summary = summary;
    	this.content = content;
    	this.completionTime = completionTime;
    }
    
    public void setFlowId(Long flowId) {
    	this.flowId = flowId;
    }
    
    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public void handleSendMessageOpenId(String openId) {
        if (StringUtils.notBlank(this.getOpenId())) {
            return;
        }
        this.openId = openId;
        this.broker.setMessageOpenId(openId);
    }

    public void clearingMessageOpenId() {
        this.openId = null;
    }
    
    public List<JSONObject> getHouseInfos4DelegationVO() {
    	if (houseInfos == null || houseInfos.size() == 0) return new ArrayList<>();
    	List<JSONObject> result = houseInfos.stream().map(houseInfo -> {
            try {
            	return JSONObject.parseObject(houseInfo);
//        		ObjectMapper mapper = new ObjectMapper();
//            	return mapper.readValue(houseInfo.toString(), DelegationVO.class);
            } catch (Exception e) {
                return null;
            }
    	}).collect(Collectors.toList());
    	return result;
    }

    public void handleSendMessage() {
        Message message = new Message(this, broker.getMessageOpenId(), broker.getPhone());
        message.handleSendMessage();
        getBean(MessageRpt.class).save(message);
    }
    
    public List<DelegationVO> getDelegationIds(String content) {
    	List<String> delegationIdStrs = new ArrayList<>();
    	// 定义正则表达式模式，用于匹配ID值
        String regex = "\"id\":\\s*(\\d+)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        // 遍历所有匹配项
        while (matcher.find()) {
        	delegationIdStrs.add(matcher.group(1));
        }
        log.info("根据任务内容[{}]查询到的房源ID列表为：{}", content, delegationIdStrs);
        List<Long> delegationIds = delegationIdStrs.stream().map(s -> {
            try {
                return Long.valueOf(s);
            } catch (NumberFormatException e) {
                return null;
            }
        }).collect(Collectors.toList());
        return getHouseInfo(delegationIds);
    }
    
    public List<DelegationVO> getHouseInfo(List<Long> delegationIds) {
    	if (delegationIds == null || delegationIds.size() == 0) return null;
        String saasKeyCode = getProperty("ejuetc.saasApi.keyCode");
        String saasKeySecret = getProperty("ejuetc.saasApi.keySecret");
        String saasHost = getProperty("ejuetc.saasApi.url");
        String consumerUrl = getProperty("ejuetc.consumer.url");
    	SaaSApiSDK sdk = new SaaSApiSDK(saasKeyCode, saasKeySecret, saasHost);
        DelegationAPI delegationAPI = sdk.feignClient(DelegationAPI.class, consumerUrl);
        ApiResponse<List<DelegationVO>> delegation = delegationAPI.query(new ApiQueryListPO().setDelegationIds(delegationIds));
        if (!delegation.isSucc() || delegation.getData() == null) return null;
        return delegation.getData();
    }

    public List<String> generatorImage(List<String> chapters) {
        String webUrl = getProperty("dcai.aixg.webUrl");
        List<String> result = new ArrayList<>(chapters.size());
        // 创建 Playwright 实例
        try (Playwright playwright = Playwright.create()) {
            // 配置浏览器选项
            BrowserType.LaunchOptions launchOptions = new BrowserType.LaunchOptions()
                    .setHeadless(true)          // 无头模式
                    .setArgs(List.of("--disable-gpu")); // GPU 加速禁用

            // 启动 Chromium 浏览器
            try (Browser browser = playwright.chromium().launch(launchOptions)) {
                // 创建浏览器上下文
                Browser.NewContextOptions contextOptions = new Browser.NewContextOptions()
                        .setViewportSize(1280, 800); // 设置视口大小

                try (BrowserContext context = browser.newContext(contextOptions)) {
                    for (String chapter : chapters) {
                        try (Page page = context.newPage()) {
                            // 访问目标 URL
                            page.navigate(webUrl + "id=" + 584 + "&page=" + chapter,
                                    new Page.NavigateOptions()
                                            .setWaitUntil(WaitUntilState.NETWORKIDLE) // 等待网络空闲
                            );
                            // 等待页面加载完成 (替代 document.readyState 检查)
                            page.waitForLoadState(LoadState.DOMCONTENTLOADED);
                            page.waitForLoadState(LoadState.LOAD);
                            page.waitForLoadState(LoadState.NETWORKIDLE);
                            // 滚动加载内容
                            scrollFullPage(page);
                            // 获取内容高度
                            int contentHeight = getContentHeight(page);

                            page.setViewportSize(1280, contentHeight);


                            byte[] screenshotBytes = page.screenshot(new Page.ScreenshotOptions()
                                    .setFullPage(true)
                                    .setType(ScreenshotType.JPEG)
                                    .setQuality(95));



                            // 上传到 OSS
                            try (InputStream is = new ByteArrayInputStream(screenshotBytes)) {
                                String imageUrl = getBean(OssComponent.class).putOSS(
                                        "task" + id + "_" + chapter + ".jpg",
                                        is,
                                        "image/jpeg"
                                );
                                result.add(imageUrl);
                            }
                        } catch (Exception e) {
                            log.error("generatorImage error for chapter: {}, error = {}", chapter, e.getMessage(), e);
                            // 错误处理策略：跳过当前章节继续处理
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Playwright initialization failed: {}", e.getMessage(), e);
            throw new BusinessException("bc.cpm.aixg.1015");
        }
        return result;
    }

    // 获取内容高度
    private int getContentHeight(Page page) {
        return (int) page.evaluate("() => {"
                + "  const content = document.querySelector('#miniapp') || document.body;"
                + "  return Math.max("
                + "    content.scrollHeight,"
                + "    content.offsetHeight,"
                + "    document.documentElement.scrollHeight"
                + "  );"
                + "}");
    }

    // 滚动加载完整页面
    private void scrollFullPage(Page page) {
        page.evaluate("async () => {"
                + "  await new Promise(resolve => {"
                + "    let currentPos = 0;"
                + "    const scrollStep = 500;"
                + "    const scrollInterval = setInterval(() => {"
                + "      const scrollHeight = document.body.scrollHeight;"
                + "      window.scrollBy(0, scrollStep);"
                + "      currentPos += scrollStep;"
                + "      "
                + "      if (currentPos >= scrollHeight) {"
                + "        clearInterval(scrollInterval);"
                + "        resolve();"
                + "      }"
                + "    }, 300);"
                + "  });"
                + "}");
        page.waitForTimeout(1000); // 额外等待
    }
    
    public WriteDetailDTO makeWriteDetailDTO() {
    	WriteDetailDTO dto = new WriteDetailDTO();
//    	dto.setArticleId(articleId);
//    	dto.setWriteId(writeId);
    	dto.setTaskStatus(status.name());
    	dto.setTaskStatusName(status.getTitle());
    	dto.setWriteType(writeType);
    	dto.setWriteTypeName(dto.buildWriteTypeName(writeType));
    	dto.setSubType(subType.name());
    	dto.setSubTypeName(subType.getTitle());
    	dto.setTitle(title);
    	dto.setAsk(ask);
    	dto.setSummary(summary);
//    	dto.setCreatedAt(createTime);
//    	dto.setCreatedAtShow(createTime);
    	dto.setContent(content);
    	return dto;
    }
}
